import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Award, 
  Users, 
  ExternalLink, 
  Bell,
  Star,
  Trophy,
  Shield,
  Zap,
  Target,
  BookOpen,
  Code,
  Lock,
  Search,
  Wifi,
  Database
} from 'lucide-react';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  duration: string;
  location: string;
  type: 'workshop' | 'ctf' | 'webinar' | 'bootcamp' | 'certification';
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  participants: number;
  maxParticipants: number;
  instructor: string;
  instructorImage: string;
  certificateType: string;
  skills: string[];
  registrationLink: string;
  featured: boolean;
  icon: React.ComponentType<{ size?: number; className?: string }>;
}

const Noticeboard: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [filter, setFilter] = useState<'all' | 'workshop' | 'ctf' | 'webinar' | 'bootcamp' | 'certification'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Sample events data
  useEffect(() => {
    const eventsData: Event[] = [
      {
        id: '1',
        title: 'Advanced Web Penetration Testing Workshop',
        description: 'Master advanced web application security testing techniques including OWASP Top 10, SQL injection, XSS, and authentication bypass methods.',
        date: '2024-01-15',
        time: '10:00 AM',
        duration: '6 hours',
        location: 'Online Live Session',
        type: 'workshop',
        level: 'Advanced',
        participants: 45,
        maxParticipants: 50,
        instructor: 'S. Tamilselvan',
        instructorImage: '/images/instructor-1.jpg',
        certificateType: 'Professional Web Security Certificate',
        skills: ['OWASP Top 10', 'SQL Injection', 'XSS', 'Authentication Bypass', 'Security Testing'],
        registrationLink: 'https://cyberwolf-career-guidance.web.app/registration.html',
        featured: true,
        icon: Shield
      },
      {
        id: '2',
        title: 'Cyber Wolf CTF Challenge 2024',
        description: 'Participate in our flagship Capture The Flag competition featuring real-world cybersecurity challenges across multiple domains.',
        date: '2024-01-20',
        time: '2:00 PM',
        duration: '8 hours',
        location: 'Online Platform',
        type: 'ctf',
        level: 'Intermediate',
        participants: 120,
        maxParticipants: 200,
        instructor: 'K. Pugazhmani',
        instructorImage: '/images/instructor-2.jpg',
        certificateType: 'CTF Participation & Achievement Certificate',
        skills: ['Cryptography', 'Forensics', 'Web Exploitation', 'Reverse Engineering', 'OSINT'],
        registrationLink: 'https://cyberwolf-career-guidance.web.app/registration.html',
        featured: true,
        icon: Trophy
      },
      {
        id: '3',
        title: 'Malware Analysis Fundamentals',
        description: 'Learn static and dynamic malware analysis techniques using industry-standard tools and methodologies.',
        date: '2024-01-25',
        time: '11:00 AM',
        duration: '4 hours',
        location: 'Hybrid (Online + Lab)',
        type: 'workshop',
        level: 'Intermediate',
        participants: 30,
        maxParticipants: 40,
        instructor: 'Dr. Cyber Expert',
        instructorImage: '/images/instructor-3.jpg',
        certificateType: 'Malware Analysis Specialist Certificate',
        skills: ['Static Analysis', 'Dynamic Analysis', 'Reverse Engineering', 'Sandbox Analysis'],
        registrationLink: 'https://cyberwolf-career-guidance.web.app/registration.html',
        featured: false,
        icon: Search
      },
      {
        id: '4',
        title: 'Network Security & Penetration Testing',
        description: 'Comprehensive training on network security assessment, vulnerability scanning, and penetration testing methodologies.',
        date: '2024-02-01',
        time: '9:00 AM',
        duration: '8 hours',
        location: 'Online Live Session',
        type: 'bootcamp',
        level: 'Advanced',
        participants: 25,
        maxParticipants: 35,
        instructor: 'K. Arun',
        instructorImage: '/images/instructor-4.jpg',
        certificateType: 'Network Security Professional Certificate',
        skills: ['Network Scanning', 'Vulnerability Assessment', 'Penetration Testing', 'Network Protocols'],
        registrationLink: 'https://cyberwolf-career-guidance.web.app/registration.html',
        featured: true,
        icon: Wifi
      },
      {
        id: '5',
        title: 'Digital Forensics Investigation Workshop',
        description: 'Hands-on training in digital forensics investigation techniques, evidence collection, and analysis procedures.',
        date: '2024-02-05',
        time: '1:00 PM',
        duration: '6 hours',
        location: 'Online Lab Environment',
        type: 'workshop',
        level: 'Intermediate',
        participants: 35,
        maxParticipants: 45,
        instructor: 'Forensics Expert',
        instructorImage: '/images/instructor-5.jpg',
        certificateType: 'Digital Forensics Investigator Certificate',
        skills: ['Evidence Collection', 'File System Analysis', 'Memory Forensics', 'Network Forensics'],
        registrationLink: 'https://cyberwolf-career-guidance.web.app/registration.html',
        featured: false,
        icon: Database
      },
      {
        id: '6',
        title: 'Cryptography & Secure Communications',
        description: 'Master modern cryptographic techniques, encryption algorithms, and secure communication protocols.',
        date: '2024-02-10',
        time: '3:00 PM',
        duration: '5 hours',
        location: 'Online Interactive Session',
        type: 'webinar',
        level: 'Advanced',
        participants: 60,
        maxParticipants: 80,
        instructor: 'Crypto Specialist',
        instructorImage: '/images/instructor-6.jpg',
        certificateType: 'Cryptography Specialist Certificate',
        skills: ['Encryption Algorithms', 'PKI', 'Digital Signatures', 'Secure Protocols'],
        registrationLink: 'https://cyberwolf-career-guidance.web.app/registration.html',
        featured: false,
        icon: Lock
      },
      {
        id: '7',
        title: 'Ethical Hacking Certification Bootcamp',
        description: 'Intensive 3-day bootcamp covering all aspects of ethical hacking with hands-on labs and certification exam.',
        date: '2024-02-15',
        time: '9:00 AM',
        duration: '3 days',
        location: 'Online Intensive Program',
        type: 'certification',
        level: 'Beginner',
        participants: 80,
        maxParticipants: 100,
        instructor: 'S. Tamilselvan',
        instructorImage: '/images/instructor-1.jpg',
        certificateType: 'Certified Ethical Hacker (CEH) Preparation Certificate',
        skills: ['Ethical Hacking', 'Penetration Testing', 'Security Assessment', 'Vulnerability Analysis'],
        registrationLink: 'https://cyberwolf-career-guidance.web.app/registration.html',
        featured: true,
        icon: Target
      },
      {
        id: '8',
        title: 'Secure Coding Practices Workshop',
        description: 'Learn secure coding practices, code review techniques, and how to prevent common security vulnerabilities.',
        date: '2024-02-20',
        time: '10:30 AM',
        duration: '4 hours',
        location: 'Online Code Lab',
        type: 'workshop',
        level: 'Intermediate',
        participants: 40,
        maxParticipants: 50,
        instructor: 'Code Security Expert',
        instructorImage: '/images/instructor-7.jpg',
        certificateType: 'Secure Development Certificate',
        skills: ['Secure Coding', 'Code Review', 'SAST/DAST', 'DevSecOps'],
        registrationLink: 'https://cyberwolf-career-guidance.web.app/registration.html',
        featured: false,
        icon: Code
      }
    ];
    setEvents(eventsData);
  }, []);

  // Filter events based on type and search term
  const filteredEvents = events.filter(event => {
    const matchesFilter = filter === 'all' || event.type === filter;
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesFilter && matchesSearch;
  });

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'workshop': return 'bg-blue-500';
      case 'ctf': return 'bg-red-500';
      case 'webinar': return 'bg-green-500';
      case 'bootcamp': return 'bg-purple-500';
      case 'certification': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner': return 'text-green-400';
      case 'Intermediate': return 'text-yellow-400';
      case 'Advanced': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Bell className="text-green-400 mr-3" size={32} />
            <h1 className="text-4xl md:text-5xl font-bold text-white font-display">
              🐺 Cyber Wolf <span className="text-green-400">Noticeboard</span>
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Join our exclusive cybersecurity events and earn professional E-Certificates! 
            Enhance your skills with hands-on workshops, CTF challenges, and expert-led sessions.
          </p>
          <div className="mt-6 flex items-center justify-center space-x-2 text-green-400">
            <Award size={20} />
            <span className="text-lg font-semibold">All Events Include E-Certificates!</span>
            <Award size={20} />
          </div>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search events, skills, or topics..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex flex-wrap gap-2">
            {['all', 'workshop', 'ctf', 'webinar', 'bootcamp', 'certification'].map((type) => (
              <button
                key={type}
                onClick={() => setFilter(type as any)}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  filter === type
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Events Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredEvents.map((event) => {
            const IconComponent = event.icon;
            return (
              <div
                key={event.id}
                className={`relative bg-gray-800 rounded-xl border border-gray-700 overflow-hidden transition-all duration-300 hover:transform hover:scale-105 hover:border-green-500 ${
                  event.featured ? 'ring-2 ring-green-500 ring-opacity-50' : ''
                }`}
              >
                {/* Featured Badge */}
                {event.featured && (
                  <div className="absolute top-4 right-4 z-10">
                    <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center">
                      <Star size={12} className="mr-1" />
                      FEATURED
                    </div>
                  </div>
                )}

                {/* Event Type Badge */}
                <div className="absolute top-4 left-4 z-10">
                  <div className={`${getTypeColor(event.type)} text-white px-3 py-1 rounded-full text-xs font-bold uppercase`}>
                    {event.type}
                  </div>
                </div>

                <div className="p-6">
                  {/* Event Icon and Title */}
                  <div className="flex items-start mb-4">
                    <div className="bg-green-500/20 p-3 rounded-lg mr-4">
                      <IconComponent size={24} className="text-green-400" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-white mb-2 line-clamp-2">
                        {event.title}
                      </h3>
                      <p className="text-gray-400 text-sm line-clamp-3">
                        {event.description}
                      </p>
                    </div>
                  </div>

                  {/* Event Details */}
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center text-gray-300 text-sm">
                      <Calendar size={16} className="mr-2 text-green-400" />
                      <span>{new Date(event.date).toLocaleDateString('en-US', { 
                        weekday: 'long', 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}</span>
                    </div>
                    
                    <div className="flex items-center text-gray-300 text-sm">
                      <Clock size={16} className="mr-2 text-green-400" />
                      <span>{event.time} • {event.duration}</span>
                    </div>
                    
                    <div className="flex items-center text-gray-300 text-sm">
                      <MapPin size={16} className="mr-2 text-green-400" />
                      <span>{event.location}</span>
                    </div>
                    
                    <div className="flex items-center text-gray-300 text-sm">
                      <Users size={16} className="mr-2 text-green-400" />
                      <span>{event.participants}/{event.maxParticipants} participants</span>
                      <span className={`ml-2 font-semibold ${getLevelColor(event.level)}`}>
                        • {event.level}
                      </span>
                    </div>
                  </div>

                  {/* Instructor */}
                  <div className="flex items-center mb-4 p-3 bg-gray-700/50 rounded-lg">
                    <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white font-bold text-sm">
                        {event.instructor.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <p className="text-white font-semibold text-sm">{event.instructor}</p>
                      <p className="text-gray-400 text-xs">Lead Instructor</p>
                    </div>
                  </div>

                  {/* Certificate Info */}
                  <div className="mb-4 p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Award size={16} className="text-green-400 mr-2" />
                      <span className="text-green-400 font-semibold text-sm">E-Certificate Included</span>
                    </div>
                    <p className="text-gray-300 text-xs">{event.certificateType}</p>
                  </div>

                  {/* Skills Tags */}
                  <div className="mb-6">
                    <div className="flex flex-wrap gap-2">
                      {event.skills.slice(0, 3).map((skill, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded-md"
                        >
                          {skill}
                        </span>
                      ))}
                      {event.skills.length > 3 && (
                        <span className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded-md">
                          +{event.skills.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Join Now Button */}
                  <a
                    href={event.registrationLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 flex items-center justify-center group"
                  >
                    <Zap size={18} className="mr-2 group-hover:animate-pulse" />
                    Join Now!
                    <ExternalLink size={16} className="ml-2 group-hover:translate-x-1 transition-transform" />
                  </a>
                </div>
              </div>
            );
          })}
        </div>

        {/* No Events Found */}
        {filteredEvents.length === 0 && (
          <div className="text-center py-12">
            <BookOpen size={64} className="text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-400 mb-2">No Events Found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
          </div>
        )}

        {/* Footer CTA */}
        <div className="mt-16 text-center bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/30 rounded-xl p-8">
          <h2 className="text-2xl font-bold text-white mb-4">
            🎯 Ready to Advance Your Cybersecurity Career?
          </h2>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            Join thousands of cybersecurity professionals who have enhanced their skills through our comprehensive training programs. 
            All events include professional E-Certificates recognized by industry leaders.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="https://cyberwolf-career-guidance.web.app/registration.html"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 flex items-center justify-center"
            >
              <Award size={20} className="mr-2" />
              Register for All Events
            </a>
            <a
              href="https://cyberwolf-career-guidance.web.app/details.html"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-gray-700 hover:bg-gray-600 text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 flex items-center justify-center"
            >
              <BookOpen size={20} className="mr-2" />
              View Course Details
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Noticeboard;
